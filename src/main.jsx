
import React from 'react';
import { createRoot, hydrateRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from '@/App.jsx';
import '@/index.css';
import { LanguageProvider } from '@/contexts/LanguageContext.jsx';
import { CurrencyProvider } from '@/contexts/CurrencyContext.jsx';
import { AuthenticationProvider } from '@/contexts/AuthenticationContext.jsx';
import { BlogProvider } from '@/contexts/BlogContext.jsx';
import { ContentProvider } from '@/contexts/ContentContext.jsx';
import { TestimonialsProvider } from '@/contexts/TestimonialsContext.jsx';
import './i18n.js';
import '@/styles/quill-overrides.css';

const APP_VERSION = '1.0.4';
const storedVersion = localStorage.getItem('appVersion');

if (typeof window !== 'undefined' && storedVersion !== APP_VERSION) {
  console.log(`App version mismatch. Stored: ${storedVersion}, New: ${APP_VERSION}. Clearing cache.`);
  localStorage.clear();
  sessionStorage.clear();
  localStorage.setItem('appVersion', APP_VERSION);
  console.log(`Cache cleared and app version updated to ${APP_VERSION}`);
}

const rootElement = document.getElementById('root');
const app = (
  <React.StrictMode>
    <BrowserRouter>
      <LanguageProvider>
        <CurrencyProvider>
          <AuthenticationProvider>
            <ContentProvider>
              <TestimonialsProvider>
                <BlogProvider>
                  <App />
                </BlogProvider>
              </TestimonialsProvider>
            </ContentProvider>
          </AuthenticationProvider>
        </CurrencyProvider>
      </LanguageProvider>
    </BrowserRouter>
  </React.StrictMode>
);

if (rootElement.hasChildNodes()) {
  hydrateRoot(rootElement, app);
} else {
  const root = createRoot(rootElement);
  root.render(app);
}
  