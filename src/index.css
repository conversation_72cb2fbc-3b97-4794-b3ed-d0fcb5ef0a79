@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

@theme {
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-gold: hsl(var(--gold));
  --color-gold-light: hsl(var(--gold-light));
  --color-gold-dark: hsl(var(--gold-dark));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  --font-sans: Inter, sans-serif;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility sidebar-tab-trigger {
  @apply flex items-center w-full p-4 text-left rounded-lg transition-all duration-200 ease-in-out;
  @apply text-gray-300 hover:bg-gray-700/60 hover:text-white;
  @apply data-[state=active]:bg-amber-500 data-[state=active]:text-black data-[state=active]:shadow-lg data-[state=active]:scale-105;
}

@utility bg-grid-pattern {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.07) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.07) 1px, transparent 1px);
  background-size: 2rem 2rem;
}

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 50%;
  }
}

@layer base {
  html {
    scroll-behavior: smooth;
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
  h1 { @apply text-4xl sm:text-5xl md:text-6xl; }
  h2 { @apply text-3xl sm:text-4xl; }
  h3 { @apply text-2xl sm:text-3xl; }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-linear-to-r from-amber-400 via-amber-500 to-yellow-600;
  }
  .gold-gradient {
    @apply bg-linear-to-r from-amber-400 via-amber-500 to-yellow-600 hover:from-amber-500 hover:to-yellow-700;
  }
  .shadow-glow {
    box-shadow: 0 0 15px 5px rgba(255, 215, 0, 0.2), 0 0 30px 10px rgba(255, 165, 0, 0.1);
  }
}

.view-toggle-btn[data-active='true'] {
  @apply bg-primary text-black;
}

.view-toggle-btn[data-active='false'] {
  @apply text-primary border-primary hover:bg-primary/10;
}

.contact-form-input {
  @apply w-full px-4 py-3 rounded-md bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:outline-hidden focus:ring-2 focus:ring-[#FFD700] focus:border-transparent transition-all duration-200;
}

.faq-sidebar-link {
  @apply flex items-center w-full px-4 py-3 text-left text-gray-300 rounded-lg transition-colors duration-200 ease-in-out;
}
.faq-sidebar-link:hover {
  @apply bg-gray-700/60 text-white;
}
.faq-sidebar-link[data-active='true'] {
  @apply bg-linear-to-r from-amber-500/20 to-amber-500/10 text-amber-300 font-semibold border-l-2 border-amber-400;
}
.faq-sidebar-link[data-active='true'] .faq-sidebar-icon {
  @apply text-amber-400;
}


/* Blog Post Content Styling */
.prose {
  color: #E5E7EB;
  max-width: none;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: #FFD700;
  font-weight: bold;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.5rem;
  line-height: 1.3;
}

.prose h3 {
  font-size: 1.25rem;
  line-height: 1.4;
}

.prose p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: #D1D5DB;
}

.prose ul, .prose ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.prose li {
  margin: 0.75rem 0;
  line-height: 1.7;
}

.prose blockquote {
  border-left: 4px solid #FFD700;
  padding: 1rem 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #FFD700;
  background-color: rgba(255, 215, 0, 0.1);
  border-radius: 0.375rem;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  font-size: 0.9rem;
}

.prose th, .prose td {
  border: 1px solid #6B7280;
  padding: 12px;
  text-align: left;
}

.prose th {
  background-color: #374151;
  color: white;
  font-weight: bold;
}

.prose tr:nth-child(even) {
  background-color: #1F2937;
}

.prose a {
  color: #FFD700;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.prose a:hover {
  color: #FFC107;
}

.prose strong {
  color: #FFD700;
  font-weight: bold;
}

.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
}

.prose code {
  background-color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #FFD700;
}

.prose pre {
  background-color: #1F2937;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  color: #E5E7EB;
}