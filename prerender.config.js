
module.exports = {
  staticDir: 'dist',
  routes: async () => {
    const fs = require('fs');
    const path = require('path');
    let blogRoutes = [];
    try {
      const blogPosts = JSON.parse(fs.readFileSync(path.resolve(__dirname, 'dist/blog-routes.json'), 'utf-8'));
      blogRoutes = blogPosts.map(post => `/blog/${post.slug}`);
    } catch (error) {
      console.warn('Could not read blog routes for prerendering, proceeding without them.', error.message);
    }
    
    return [
      '/',
      '/services',
      '/land-arrangement-umrah',
      '/pricing',
      '/about',
      '/contact',
      '/blog',
      '/faq',
      '/testimonials',
      '/order',
      ...blogRoutes,
    ];
  },
  renderer: '@prerenderer/renderer-puppeteer',
  rendererOptions: {
    maxConcurrentRoutes: 5,
    renderAfterElementExists: '#root > div',
    inject: {},
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
  },
  postProcess(renderedRoute) {
    renderedRoute.html = renderedRoute.html
      .replace(/<script[^>]*>/g, (match) => {
        if (match.includes(' type="application/ld+json"')) {
          return match;
        }
        if (match.includes(' defer') || match.includes(' async')) {
          return match;
        }
        return match.replace('>', ' defer>');
      })
      .replace('id="root"', 'id="root" data-prerendered="true"');
    return renderedRoute;
  }
};
  