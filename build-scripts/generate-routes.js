
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const postsFilePath = path.resolve(__dirname, '../src/data/blogPosts.js');
const outputDir = path.resolve(__dirname, '../dist');
const outputPath = path.resolve(outputDir, 'blog-routes.json');

async function generateBlogRoutes() {
  try {
    const { initialBlogPosts } = await import(postsFilePath);
    
    const routes = initialBlogPosts.map(post => ({ slug: post.slug }));

    if (!fs.existsSync(outputDir)){
        fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(outputPath, JSON.stringify(routes, null, 2));
    console.log(`Successfully generated ${routes.length} blog routes for prerendering.`);
  } catch (error) {
    console.error('Error generating blog routes:', error);
    // Create an empty file to prevent build from failing
    if (!fs.existsSync(outputDir)){
        fs.mkdirSync(outputDir, { recursive: true });
    }
    fs.writeFileSync(outputPath, JSON.stringify([]));
    console.log('Created empty blog-routes.json to allow build to continue.');
  }
}

generateBlogRoutes();
  